{layout name="layout1" /}
<!-- 样式 -->
<style>
  .layui-table-cell {
    height: auto;
  }

  /* 内联编辑样式 */
  .name-text:hover {
    background-color: #f0f9ff;
    border: 1px dashed #009688;
  }

  .sort-display:hover {
    background-color: #e8f5e8;
    border-color: #009688 !important;
  }

  .name-input, .sort-input {
    border: 1px solid #009688;
    box-shadow: 0 0 5px rgba(0, 150, 136, 0.3);
  }

  .name-input:focus, .sort-input:focus {
    border-color: #009688;
    box-shadow: 0 0 8px rgba(0, 150, 136, 0.5);
    outline: none;
  }

  .name-text {
    display: inline-block;
    vertical-align: middle;
    transition: all 0.2s ease;
    min-height: 20px;
    line-height: 20px;
  }
</style>
<!-- 操作提示 -->
<div class="layui-fluid">
  <div class="layui-card" style="margin-top: 15px;">
    <div class="layui-card-body">
      <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
        <div class="layui-colla-item">
          <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
          <div class="layui-colla-content layui-show">
            <p>*平台商品分类，商家发布商品的时候需要选择对应的平台商品分类，用户可以根据商品分类搜索商品。</p>
            <p style="color: #009688;">*快速编辑：双击分类名称可直接编辑，点击排序数字可直接编辑，按Enter保存，按Esc取消。</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 功能按钮 -->
    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
      <div class="layui-form-item">
        <div class="layui-btn-container" style="display: inline-block;">
          <div class="layui-btn-group">
            <button class="layui-btn layui-btn-sm layui-btn-goods_category {$view_theme_color}"
              id="goods_category-add">新增商品分类</button>
            <button class="layui-btn layui-btn-sm  layui-btn-goods_category {$view_theme_color}"
              id="expand-all">全部展开</button>
            <button class="layui-btn layui-btn-sm  layui-btn-goods_category {$view_theme_color}"
              id="fold-all">全部折叠</button>
          </div>
        </div>
        <input type="text" id="search-value" placeholder="分类名称" autocomplete="off" class="layui-input"
          style="display: inline-block;width: 140px;height: 30px;padding: 0 5px;margin-right: 5px;">
        <div class="layui-btn-container" style="display: inline-block;">
          <button id="search" class="layui-btn layui-btn-sm  layui-btn-goods_category {$view_theme_color}">
            <i class="layui-icon">&#xe615;</i>搜索
          </button>
        </div>
      </div>
    </div>
    
    <div class="layui-card-body">
      <!-- 树形表格 -->
      <table id="goods_category-lists" lay-filter="goods_category-lists"></table>
      <!-- 分类图标 -->
      <script type="text/html" id="image">
        {{#  if(d.image != ''){ }}
          <img src="{{d.image}}" style="height:80px;width:80px" class="image-show">
        {{#  } }} 
      </script>
      <!-- 商家端可选 -->
      <script type="text/html" id="shop_visible">
        <input type="checkbox"  lay-filter="switch-shop_visible" data-id={{d.id}} data-field='shop_visible'   lay-skin="switch" lay-text="可选|不可选" {{#  if(d.shop_visible){ }} checked  {{# } }} />
      </script>
      <!-- API显示 -->
      <script type="text/html" id="api_visible">
        <input type="checkbox"  lay-filter="switch-api_visible" data-id={{d.id}} data-field='api_visible'   lay-skin="switch" lay-text="显示|隐藏" {{#  if(d.api_visible){ }} checked  {{# } }} />
      </script>
      <!-- 关联资质 -->
      <script type="text/html" id="qualification">
        {{#  if(d.qualification_names){ }}
          <span style="color: #5FB878; font-size: 12px;">{{d.qualification_names}}</span>
        {{#  } else { }}
          <span style="color: #999; font-size: 12px;">未关联资质</span>
        {{#  } }}
      </script>
      <!-- 分类名称内联编辑模板 -->
      <script type="text/html" id="name-edit">
        <span class="name-text" style="cursor: text; padding: 2px 4px; border-radius: 3px;" title="双击编辑名称">{{d.name}}</span>
        <input type="text" class="name-input layui-input" style="display: none; height: 30px; padding: 5px; margin-top: 2px;" value="{{d.name}}" data-id="{{d.id}}">
      </script>
      <!-- 排序内联编辑模板 -->
      <script type="text/html" id="sort-edit">
        <span class="sort-display" style="cursor: pointer; padding: 3px 6px; display: inline-block; min-width: 30px; text-align: center; border: 1px solid transparent; border-radius: 3px; background: #f8f8f8;" title="点击编辑排序">{{d.sort}}</span>
        <input type="number" class="sort-input layui-input" style="display: none; height: 28px; width: 50px; text-align: center; padding: 3px;" value="{{d.sort}}" data-id="{{d.id}}">
      </script>
      <!-- 操作列 -->
      <script type="text/html" id="goods_category-operation">
        <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
      </script>
    </div>
  </div>
</div>

<script>
  layui.config({
    version: "{$front_version}",
    base: '/static/lib/'
  }).extend({
    treeTable: 'treetable/treeTable'
  }).use(['layer', 'treeTable', 'form', 'element'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var treeTable = layui.treeTable;

    // 渲染树形表格
    var insTb = treeTable.render({
      elem: '#goods_category-lists',
      tree: {
        iconIndex: 0, // 折叠图标显示在第几列
        childName: 'sub', // 设定children的字段名，pid形式数据不需要
        getIcon: function (d) {  // 自定义图标
          return '<i class="ew-tree-icon layui-icon layui-icon-spread-left "></i>';
        }
      },
      cols: [
        { field: 'name', title: '分类名称', width: 280, templet: '#name-edit'},
        { title: '分类图标', width: 120, align: 'center', templet: '#image'},
        { field: 'qualification_names', title: '关联资质', width: 200, align: 'center', templet: '#qualification'},
        { title: '商家可选', width: 100, align: 'center', templet: '#shop_visible' },
        { title: 'API显示', width: 100, align: 'center', templet: '#api_visible' },
        { field: 'sort', title: '排序', width: 80, align: 'center', templet: '#sort-edit' },
        { fixed: 'right', align: 'center', toolbar: '#goods_category-operation', title: '操作'}
      ],
      reqData: function(data, callback) {
        // 在这里写ajax请求，通过callback方法回调数据
        like.ajax({
          url:'{:url("goods.category/lists")}',
          type:'get',
          success:function (res) {
            // 转json对象
            jsonObj = JSON.parse(res.data);
            if(res.code==0) callback(jsonObj);
            else callback(res.msg);
          }
        })
      }
    });

    // 新增商品分类
    $('#goods_category-add').click(function () {
      layer.open({
        type: 2
        , title: '新增商品分类'
        , content: '{:url("goods.category/add")}'
        , area: ['90%', '90%']
        , btn: ['确认', '返回']
        , btnAlign: 'c'
        , yes: function (index, layero) {
          var iframeWindow = window['layui-layer-iframe' + index]
          , submitID = 'add-goods_category-submit'
          , submit = layero.find('iframe').contents().find('#' + submitID);

          //监听提交
          iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
            var field = data.field; 
            console.log(data);
            like.ajax({
              url: '{:url("goods.category/add")}',
              data: field,
              type: "post",
              success: function (res) {
                if (res.code == 1) {
                  layui.layer.msg(res.msg, {
                    offset: '15px'
                    , icon: 1
                    , time: 1000
                  });
                  layer.close(index); //关闭弹层
                  //location.reload();//刷新
                }
              }
            });
          });
          // 触发子窗口表单提交事件
          submit.trigger('click');
        }
      })
    });

    // 监听行工具条事件
    treeTable.on('tool(goods_category-lists)', function (obj) {
      var event = obj.event;
      if (event === 'del') {
        layer.confirm('确定删除商品分类:' + '<span style="color: red">' + obj.data.name + '</span>', function (index) {
          like.ajax({
            url: '{:url("goods.category/del")}',
            data: { id: obj.data.id },
            type: 'post',
            dataType: 'json',
            success: function (res) {
              if (res.code === 1) {
                layui.layer.msg(res.msg, {
                  offset: '15px'
                  , icon: 1
                  , time: 1000
                },function() {
                  layer.close(index); //关闭弹层
                  //location.reload();//刷新
                });
              }
            }
          })
        })
      }

      if (event === 'edit') {
        layer.open({
          type: 2
          , title: '编辑商品分类'
          , content: '{:url("goods.category/edit")}?id=' + obj.data.id
          , area: ['90%', '90%']
          , btn: ['确定', '取消']
          , btnAlign: 'c'
          , yes: function (index, layero) {
            var iframeWindow = window['layui-layer-iframe' + index]
              , submitID = 'edit-goods_category-submit'
              , submit = layero.find('iframe').contents().find('#' + submitID);

            //监听提交
            iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
              var field = data.field; //获取提交的字段
              like.ajax({
                url: '{:url("goods.category/edit")}',
                data: field,
                type: "post",
                success: function (res) {
                  if (res.code == 1) {
                    layui.layer.msg(res.msg, {
                      offset: '15px'
                      , icon: 1
                      , time: 1000
                    }, function() {
                      layer.close(index); //关闭弹层
                      //location.reload();//刷新
                    });
                  }
                }
              });
            });
            submit.trigger('click');
          }
        })
      }
    });

    // 商家端可选切换
    form.on('switch(switch-shop_visible)', function (obj) {
      var id = obj.elem.attributes['data-id'].nodeValue
      var status = 0;
      if (this.checked) {
        status = 1;
      }
      like.ajax({
        url: '{:url("goods.category/switchShopVisible")}',
        data: { id: id, status: status },
        type: 'post',
        success: function (res) {
          if (res.code == 1) {
            layui.layer.msg(res.msg, {
              offset: '15px'
              , icon: 1
              , time: 1000
            });
          }else{
            layui.layer.msg(res.msg, {
              offset: '15px'
              , icon: 2
              , time: 1000
            }, function() {
             // location.reload();//刷新
            });
          }
        }
      })
    })

    // API显示切换
    form.on('switch(switch-api_visible)', function (obj) {
      var id = obj.elem.attributes['data-id'].nodeValue
      var status = 0;
      if (this.checked) {
        status = 1;
      }
      like.ajax({
        url: '{:url("goods.category/switchApiVisible")}',
        data: { id: id, status: status },
        type: 'post',
        success: function (res) {
          if (res.code == 1) {
            layui.layer.msg(res.msg, {
              offset: '15px'
              , icon: 1
              , time: 1000
            });
          }else{
            layui.layer.msg(res.msg, {
              offset: '15px'
              , icon: 2
              , time: 1000
            }, function() {
             // location.reload();//刷新
            });
          }
        }
      })
    })

    // 全部展开
    $('#expand-all').click(function () {
      insTb.expandAll();
    });

    // 全部折叠
    $('#fold-all').click(function () {
      insTb.foldAll();
    });

    //搜索
    $('#search').click(function () {
      var keywords = $('#search-value').val();
      if (keywords) {
        insTb.filterData(keywords);
      } else {
        insTb.clearFilter();
      }
    });

    // 内联编辑功能
    // 分类名称双击编辑
    $(document).on('dblclick', '.name-text', function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      e.preventDefault(); // 阻止默认行为

      var $this = $(this);
      // 使用更robust的选择器，先尝试siblings，如果找不到就在父容器中查找
      var $input = $this.siblings('.name-input');
      if ($input.length === 0) {
        $input = $this.parent().find('.name-input');
      }

      console.log('双击编辑 - span:', $this.length, 'input:', $input.length); // 调试信息

      // 确保元素存在且可见
      if ($this.length && $input.length) {
        $this.hide();
        $input.show().focus().select();
      } else {
        console.log('未找到对应的input元素'); // 调试信息
      }
    });

    // 排序点击编辑
    $(document).on('click', '.sort-display', function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      var $this = $(this);
      var $input = $this.siblings('.sort-input');
      $this.hide();
      $input.show().focus().select();
    });

    // 分类名称失去焦点保存
    $(document).on('blur', '.name-input', function() {
      var $this = $(this);
      // 使用更robust的选择器
      var $nameText = $this.siblings('.name-text');
      if ($nameText.length === 0) {
        $nameText = $this.parent().find('.name-text');
      }

      var newValue = $this.val().trim();
      var id = $this.data('id');
      var oldValue = $nameText.text().trim();

      if (newValue && newValue !== oldValue) {
        // 调用保存接口
        saveFieldValue(id, 'name', newValue, $this, $nameText, null);
      } else {
        // 恢复显示 - 确保元素正确显示
        $this.hide();
        if ($nameText.length) {
          $nameText.show();
        }
      }
    });

    // 排序失去焦点保存
    $(document).on('blur', '.sort-input', function() {
      var $this = $(this);
      var $display = $this.siblings('.sort-display');
      var newValue = parseInt($this.val()) || 0;
      var id = $this.data('id');
      var oldValue = parseInt($display.text()) || 0;

      if (newValue !== oldValue) {
        // 调用保存接口
        saveFieldValue(id, 'sort', newValue, $this, $display, null);
      } else {
        // 恢复显示
        $this.hide();
        $display.show();
      }
    });

    // 按Enter键保存
    $(document).on('keypress', '.name-input, .sort-input', function(e) {
      if (e.which === 13) { // Enter键
        $(this).blur();
      }
    });

    // 按Esc键取消编辑
    $(document).on('keyup', '.name-input', function(e) {
      if (e.which === 27) { // Esc键
        var $this = $(this);
        var $nameText = $this.siblings('.name-text');
        $this.hide();
        if ($nameText.length) {
          $nameText.show();
        }
      }
    });

    $(document).on('keyup', '.sort-input', function(e) {
      if (e.which === 27) { // Esc键
        var $this = $(this);
        var $display = $this.siblings('.sort-display');
        $this.hide();
        $display.show();
      }
    });

    // 保存字段值的通用函数
    function saveFieldValue(id, field, value, $input, $display, $editIcon) {
      console.log('保存字段:', {id: id, field: field, value: value}); // 调试信息

      // 使用jQuery原生AJAX，避免like.ajax可能的问题
      $.ajax({
        url: '{:url("goods.category/updateField")}',
        data: {
          id: id,
          field: field,
          value: value
        },
        type: 'post',
        dataType: 'json',
        success: function (res) {
          console.log('服务器响应:', res); // 调试信息
          console.log('res.code类型:', typeof res.code, 'res.code值:', res.code); // 调试信息

          // 确保类型匹配，使用严格相等或转换类型
          if (res.code == 1 || res.code === 1 || parseInt(res.code) === 1) {
            console.log('进入成功分支'); // 调试信息
            // 更新显示值
            $display.text(value);
            $input.hide();
            $display.show();

            // 更新treeTable中的数据
            updateTableData(id, field, value);

            layui.layer.msg('保存成功', {
              offset: '15px',
              icon: 1,
              time: 1000
            });
          } else {
            console.log('进入失败分支'); // 调试信息
            layui.layer.msg(res.msg || '保存失败', {
              offset: '15px',
              icon: 2,
              time: 2000
            });
            // 恢复原值
            $input.hide();
            $display.show();
          }
        },
        error: function(xhr, status, error) {
          console.error('AJAX错误:', {xhr: xhr, status: status, error: error}); // 调试信息
          layui.layer.msg('网络错误，保存失败', {
            offset: '15px',
            icon: 2,
            time: 2000
          });
          // 恢复原值
          $input.hide();
          $display.show();
        }
      });
    }

    // 更新表格数据
    function updateTableData(id, field, value) {
      // 获取当前表格数据
      var tableData = insTb.config.data;

      // 递归查找并更新数据
      function updateNode(nodes) {
        for (var i = 0; i < nodes.length; i++) {
          if (nodes[i].id == id) {
            nodes[i][field] = value;
            return true;
          }
          if (nodes[i].sub && nodes[i].sub.length > 0) {
            if (updateNode(nodes[i].sub)) {
              return true;
            }
          }
        }
        return false;
      }

      updateNode(tableData);
    }
  });
</script>